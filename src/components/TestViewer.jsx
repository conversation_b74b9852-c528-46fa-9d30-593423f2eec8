import { useEffect, useRef, useState } from 'react';

const TestViewer = ({ pdfUrl, extractedData }) => {
  const containerRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pdfInstance, setPdfInstance] = useState(null);
  const [showCircles, setShowCircles] = useState(true);
  const [polygonAnalysisResults, setPolygonAnalysisResults] = useState([]);

  useEffect(() => {
    const container = containerRef.current;
    let cleanup = () => { };

    (async () => {
      console.log('TestViewer mounted');
      console.log('Container:', container);
      console.log('PDF URL:', pdfUrl);

      if (!container || !pdfUrl) {
        console.log('No container or PDF URL available');
        setError('No PDF URL provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const NutrientViewer = (await import('@nutrient-sdk/viewer')).default;
        console.log('NutrientViewer imported:', NutrientViewer);

        // Ensure there's only one NutrientViewer instance
        NutrientViewer.unload(container);

        const loadConfig = {
          container,
          document: pdfUrl,
          baseUrl: `${window.location.protocol}//${window.location.host}/`,
          // To remove "FOR EVALUATION PURPOSE ONLY" watermark, add your license key:
          // licenseKey: 'YOUR_LICENSE_KEY_HERE'
        };

        console.log('Load config:', loadConfig);

        const instance = await NutrientViewer.load(loadConfig);
        console.log('PDF loaded successfully:', instance);
        setPdfInstance(instance);
        setIsLoading(false);

        // Set up polygon annotation event listener
        setupPolygonEventListener(instance);

        cleanup = () => {
          NutrientViewer.unload(container);
        };
      } catch (error) {
        console.error('Error loading PDF:', error);
        console.error('Error stack:', error.stack);
        console.error('Error name:', error.name);
        setError(`Failed to load PDF: ${error.message}`);
        setIsLoading(false);
      }
    })();

    return cleanup;
  }, [pdfUrl]);

  // Function to set up polygon annotation event listener
  const setupPolygonEventListener = (instance) => {
    instance.addEventListener('annotations.create', async (annotations) => {
      for (const annotation of annotations) {
        if (annotation instanceof window.PSPDFKit.Annotations.PolygonAnnotation) {
          console.log('Polygon annotation created:', annotation);
          await handlePolygonCreated(annotation, instance);
        }
      }
    });
  };

  // Function to check if a point is inside a polygon using ray casting algorithm
  const isPointInPolygon = (point, polygon) => {
    const [x, y] = point;
    let inside = false;

    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const [xi, yi] = polygon[i];
      const [xj, yj] = polygon[j];

      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }

    return inside;
  };

  // Function to handle polygon creation and perform spatial analysis
  const handlePolygonCreated = async (polygonAnnotation, instance) => {
    try {
      // Get page info for coordinate conversion
      const pageInfo = await instance.pageInfoForIndex(0);
      const pageHeight = pageInfo.height;

      // Extract polygon points and convert coordinates
      const polygonPoints = polygonAnnotation.points.map(point => {
        // Convert from Nutrient SDK coordinates to PDF coordinates
        // Nutrient SDK: origin at top-left, Y increases downward
        // PDF: origin at bottom-left, Y increases upward
        const pdfX = point.x;
        const pdfY = pageHeight - point.y; // Flip Y coordinate
        return [pdfX, pdfY];
      });

      console.log('Polygon points in PDF coordinates:', polygonPoints);

      // Check which labels are inside the polygon
      const labelsInside = [];
      if (extractedData && extractedData.filtered_pairs) {
        for (const pair of extractedData.filtered_pairs) {
          const { x0, y0, x1, y1 } = pair.bbox;

          // Calculate center point of the bounding box
          const centerX = (x0 + x1) / 2;
          const centerY = (y0 + y1) / 2;

          // Check if center point is inside polygon
          if (isPointInPolygon([centerX, centerY], polygonPoints)) {
            labelsInside.push(pair);
            console.log(`Label "${pair.label}" (${pair.code}) is inside polygon`);
          }
        }
      }

      // Highlight the labels that are inside the polygon
      if (labelsInside.length > 0) {
        await highlightLabelsInPolygon(labelsInside, instance, pageHeight);
        setPolygonAnalysisResults(prev => [...prev, {
          polygonId: polygonAnnotation.id,
          labelsInside: labelsInside,
          polygonPoints: polygonPoints
        }]);
      }

      console.log(`Found ${labelsInside.length} labels inside the polygon`);

    } catch (error) {
      console.error('Error handling polygon creation:', error);
    }
  };

  // Function to highlight labels that are inside the polygon
  const highlightLabelsInPolygon = async (labelsInside, instance, pageHeight) => {
    try {
      // Import PSPDFKit classes
      const NutrientViewer = (await import('@nutrient-sdk/viewer')).default;
      const PSPDFKit = NutrientViewer.PSPDFKit || window.PSPDFKit;

      // Create highlight annotations for each label
      const highlightAnnotations = labelsInside.map((pair, index) => {
        const { x0, y0, x1, y1 } = pair.bbox;

        // Convert PDF coordinates to Nutrient SDK coordinates
        const convertedX0 = x0;
        const convertedY0 = pageHeight - y1; // Flip Y coordinate (y1 is top in PDF)
        const convertedX1 = x1;
        const convertedY1 = pageHeight - y0; // Flip Y coordinate (y0 is bottom in PDF)

        return new PSPDFKit.Annotations.RectangleAnnotation({
          pageIndex: 0,
          boundingBox: new PSPDFKit.Geometry.Rect({
            left: Math.min(convertedX0, convertedX1),
            top: Math.min(convertedY0, convertedY1),
            width: Math.abs(convertedX1 - convertedX0),
            height: Math.abs(convertedY1 - convertedY0),
          }),
          strokeColor: new PSPDFKit.Color({ r: 0, g: 255, b: 0 }), // Green color
          fillColor: new PSPDFKit.Color({ r: 0, g: 255, b: 0, a: 0.3 }), // Semi-transparent green
          strokeWidth: 3,
          id: `polygon-highlight-${pair.code}-${index}`,
          name: `Highlight for ${pair.label}`,
          note: `${pair.label} (${pair.code}) - Found in polygon analysis`
        });
      });

      // Add highlight annotations to the PDF
      await instance.create(highlightAnnotations);
      console.log('Successfully added', highlightAnnotations.length, 'highlight annotations');

    } catch (error) {
      console.error('Error highlighting labels:', error);
    }
  };

  // Function to plot red circles at x0,y0 coordinates
  const plotRedCircles = async () => {
    if (!pdfInstance || !extractedData || !extractedData.filtered_pairs) {
      return;
    }

    try {
      console.log('Plotting red circles for', extractedData.filtered_pairs.length, 'coordinates');

      // Clear existing circle annotations first
      const existingAnnotations = await pdfInstance.getAnnotations(0);
      const circleAnnotations = existingAnnotations.filter(ann => ann.id && ann.id.startsWith('circle-'));
      if (circleAnnotations.length > 0) {
        await pdfInstance.delete(circleAnnotations);
        console.log('Cleared', circleAnnotations.length, 'existing circle annotations');
      }

      // Get page info to understand the coordinate system
      const pageInfo = await pdfInstance.pageInfoForIndex(0);
      const pageHeight = pageInfo.height;

      console.log('Page dimensions:', pageInfo.width, 'x', pageInfo.height);

      // Import PSPDFKit classes - use the same import as the main viewer
      const NutrientViewer = (await import('@nutrient-sdk/viewer')).default;
      const PSPDFKit = NutrientViewer.PSPDFKit || window.PSPDFKit;

      // Create annotations for each coordinate
      const annotations = extractedData.filtered_pairs.map((pair, index) => {
        const { x0, y0 } = pair.bbox;

        // Convert PDF coordinates to Nutrient SDK coordinates
        // PDF: origin at bottom-left, Y increases upward
        // Nutrient SDK: origin at top-left, Y increases downward
        const convertedX = x0;
        const convertedY = pageHeight - y0; // Flip Y coordinate

        console.log(`Converting coordinates for ${pair.label}: PDF(${x0}, ${y0}) -> SDK(${convertedX}, ${convertedY})`);

        // Create a small circle (ellipse) at converted coordinates
        // The circle size is 10x10 pixels in PDF coordinates
        const circleSize = 10;

        return new PSPDFKit.Annotations.EllipseAnnotation({
          pageIndex: 0, // Assuming first page
          boundingBox: new PSPDFKit.Geometry.Rect({
            left: convertedX - circleSize / 2,
            top: convertedY - circleSize / 2,
            width: circleSize,
            height: circleSize,
          }),
          strokeColor: new PSPDFKit.Color({ r: 255, g: 0, b: 0 }), // Red color
          fillColor: new PSPDFKit.Color({ r: 255, g: 0, b: 0 }), // Red fill
          strokeWidth: 2,
          opacity: 0.8,
          id: `circle-${index}`,
          name: `Circle for ${pair.label}`,
          note: `${pair.label} (${pair.code})`
        });
      });

      // Add all annotations to the PDF
      await pdfInstance.create(annotations);
      console.log('Successfully added', annotations.length, 'red circle annotations');

    } catch (error) {
      console.error('Error plotting red circles:', error);
    }
  };

  // Function to clear all circle annotations
  const clearCircles = async () => {
    if (!pdfInstance) return;

    try {
      const existingAnnotations = await pdfInstance.getAnnotations(0);
      const circleAnnotations = existingAnnotations.filter(ann => ann.id && ann.id.startsWith('circle-'));
      if (circleAnnotations.length > 0) {
        await pdfInstance.delete(circleAnnotations);
        console.log('Cleared all circle annotations');
      }
    } catch (error) {
      console.error('Error clearing circles:', error);
    }
  };

  // Effect to plot/clear circles when extractedData or showCircles changes
  useEffect(() => {
    if (pdfInstance && extractedData) {
      if (showCircles) {
        plotRedCircles();
      } else {
        clearCircles();
      }
    }
  }, [pdfInstance, extractedData, showCircles]);

  if (!pdfUrl) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-gray-600">No PDF selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      {/* Toggle button for showing/hiding circles */}
      {extractedData && extractedData.filtered_pairs && extractedData.filtered_pairs.length > 0 && (
        <div className="absolute top-4 left-4 z-50">
          <button
            onClick={() => setShowCircles(!showCircles)}
            className={`${showCircles
              ? 'bg-red-600 hover:bg-red-700'
              : 'bg-gray-600 hover:bg-gray-700'
              } text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 shadow-lg`}
          >
            <div className={`w-3 h-3 rounded-full ${showCircles ? 'bg-white' : 'bg-red-400'}`}></div>
            {showCircles ? 'Hide Circles' : 'Show Circles'}
            <span className="text-xs bg-black bg-opacity-20 px-2 py-1 rounded">
              {extractedData.filtered_pairs.length}
            </span>
          </button>
        </div>
      )}

      {/* Polygon Analysis Results */}
      {polygonAnalysisResults.length > 0 && (
        <div className="absolute top-4 left-4 z-50 mt-16">
          <div className="bg-white rounded-lg shadow-lg p-4 max-w-sm max-h-64 overflow-y-auto">
            <h3 className="text-sm font-semibold text-gray-800 mb-2 flex items-center gap-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Polygon Analysis Results
            </h3>
            {polygonAnalysisResults.map((result, index) => (
              <div key={index} className="mb-3 p-2 bg-green-50 rounded border-l-4 border-green-400">
                <p className="text-xs text-gray-600 mb-1">Polygon {index + 1}</p>
                <p className="text-sm font-medium text-green-800">
                  {result.labelsInside.length} labels found
                </p>
                <div className="mt-1 space-y-1">
                  {result.labelsInside.map((label, labelIndex) => (
                    <div key={labelIndex} className="text-xs text-gray-700 bg-white px-2 py-1 rounded">
                      <span className="font-medium">{label.label}</span>
                      <span className="text-gray-500 ml-1">({label.code})</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            <button
              onClick={() => setPolygonAnalysisResults([])}
              className="text-xs text-gray-500 hover:text-gray-700 mt-2"
            >
              Clear Results
            </button>
          </div>
        </div>
      )}

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="text-center">
            <svg className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <p className="text-gray-600">Loading PDF...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 z-10">
          <div className="text-center">
            <svg className="w-16 h-16 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-red-600 mb-2">Error loading PDF</p>
            <p className="text-sm text-red-500">{error}</p>
          </div>
        </div>
      )}

      <div
        ref={containerRef}
        className="w-full h-full"
        style={{
          width: '100vw',
          height: '100vh'
        }}
      />
    </div>
  );
};

export default TestViewer;
